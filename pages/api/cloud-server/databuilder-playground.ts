/**
 * @file DataBuilder Playground API 代理，支持跨域访问
 */

import {NextApiRequest, NextApiResponse} from 'next';
import axios from 'axios';

// 设置 CORS 头部
const setCorsHeaders = (res: NextApiResponse) => {
    res.setHeader('Access-Control-Allow-Origin', 'https://cloud.baidu.com');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-region');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
};

// 创建axios实例
const dataBuilderApi = axios.create({
    baseURL: 'https://console.bce.baidu.com',
    headers: {
        'x-region': 'bd',
        'Content-Type': 'application/json'
    }
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    // 设置 CORS 头部
    setCorsHeaders(res);

    // 处理 OPTIONS 预检请求
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    const {action} = req.query;

    try {
        if (action === 'active') {
            // 获取playground激活状态
            if (req.method !== 'GET') {
                res.status(405).json({error: 'Method not allowed'});
                return;
            }

            const response = await dataBuilderApi.get('/api/databuilder/v1/playground/active');
            res.status(200).json(response.data);

        } else if (action === 'activate') {
            // 激活用户体验账户
            if (req.method !== 'POST') {
                res.status(405).json({error: 'Method not allowed'});
                return;
            }

            const response = await dataBuilderApi.post('/api/databuilder/v1/playground');
            res.status(200).json(response.data);

        } else {
            res.status(404).json({error: 'Action not found'});
        }

    } catch (error: any) {
        console.error('DataBuilder Playground API 错误:', error);
        
        // 返回详细的错误信息
        const statusCode = error.response?.status || 500;
        const errorMessage = error.response?.data || error.message || 'Internal server error';
        
        res.status(statusCode).json({
            error: 'API request failed',
            details: errorMessage
        });
    }
}
