/* eslint-disable max-len */
/* eslint-disable max-lines */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable react/jsx-closing-tag-location */
/* eslint-disable */
import {useState, useEffect, useCallback, useMemo, useRef} from 'react';

// next.js 相关
import {GetServerSideProps} from 'next'; // 服务器渲染获取数据
import Head from 'next/head'; // SEO 相关 使用HEAD标签内部设置标题、描述等SEO相关内容

// 官网开发需要
import {sendMonitor} from '@common/helper/page';
import {useCheckUserinfo} from '@common/hooks/useCheckUserinfo';

import {PagePropsObj} from '@common/interface/common';

// 工具
import {throttle} from 'lodash';
import {message} from 'antd';

// css Modules相关
import cx from 'classnames';
import styles from './index_1200.module.less';

// 获取设备信息
import useDeviceType from '@components/database/hooks/useDeviceType';
import {getPlaygroundActive, activatePlayground} from './api';

// 服务和组件
import {Ioc} from '@baidu/bce-decorators';
import {netService, UDynamicService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {URealNameModal} from '@common/components/URealNameModal/URealNameModal';
import {UConfirm} from '@common/components/UConfirm/UConfirm';


// 模块类
const moduleClassName = 'databuilder-module';
// 模块标题类
const moduleTitleClassName = 'databuilder-module-title';
// 模块内容类
const moduleContentClassName = 'databuilder-module-content';
const basePath = 'https://front-end.bj.bcebos.com/1.1.4';
enum ModuleKey {
    // 产品架构
    Structure,
    // 核心能力
    Ability,
    // 应用场景
    Scene,
    // 使用引导
    Guide
}
// 设备类型
enum DeviceType {
    /** 中小尺寸移动设备 */
    SmMdMobile,
    Mobile,
    Tablet,
    Desktop
}
// 页面配置
const config = {
    freeBtnText: '立即使用',
    playBtnText: 'PlayGround',
    queryBtnText: '私有化部署咨询',
    docBtnText: '帮助文档',
    // 吸顶header
    header: {
        productName: '千帆数据智能平台 DataBuilder',
        moduleList: [
            {
                name: '产品架构',
                key: ModuleKey.Structure,
            },
            {
                name: '核心能力',
                key: ModuleKey.Ability,
            },
            {
                name: '应用场景',
                key: ModuleKey.Scene,
            },
            {
                name: '使用引导',
                key: ModuleKey.Guide,
            },
        ],
    },
    banner: {
        title: '千帆数据智能平台 DataBuilder',
        description: '提供多模态数据管理、高性能计算、智能开发平台和丰富算子能力，支持用户完成一站式数据治理、数据加工和数据应用，帮助用户提升模型训练和AI原生应用迭代效率',
        advantageList: [
            {
                highlightBefore: 'Data +',
                title: '',
                highlightText: 'AI',
                description: '支持基于数据搭建大模型原生AI应用，全程保障用户数据隐私与自主管控',
            },
            {
                highlightBefore: '',
                title: '效率提升',
                highlightText: '6倍',
                description: '统一管理数据和AI资产，高效数据协同开发，提升端到端的数据迭代效率',
            },
            {
                highlightBefore: '',
                title: '成本降低',
                highlightText: '40%',
                description: 'GPU/CPU异构算力混合调度，一站式多引擎计算能力，显著降低资源成本',
            },
            {
                highlightBefore: '智能',
                title: '数据洞察',
                highlightText: '',
                description: '自然语言交互，帮助用户轻松搭建业务数据看板，挖掘数据价值',
            },
        ],
    },
    structure: {
        title: '能力覆盖数据全生命周期',
        imgSrc: `${basePath}/img_databuilder_jiagoutu.png`,
    },
    ability: {
        title: '核心能力',
        // 高效数据迭代
        iteration: {
            title: '高效数据迭代',
            cardList: [
                {
                    title: '在线开发平台',
                    icon: `${basePath}/icon_gaoxiaoshuju_01.png`,
                    description: '同个工作空间多角色协作，支持在线文件系统等能力，提供统一的开发体验',
                },
                {
                    title: '灵活任务编排',
                    icon: `${basePath}/icon_gaoxiaoshuju_02.png`,
                    description: '支持多任务可视化调度和分布式并行，便于用户完成复杂的数据开发工作',
                },
                {
                    title: '优质算子增强',
                    icon: `${basePath}/icon_gaoxiaoshuju_03.png`,
                    description: '内置增强算子和高质量数据集，有助于提升开发效率和效果',
                },
                {
                    title: 'Agent开发提效',
                    icon: `${basePath}/icon_gaoxiaoshuju_04.png`,
                    description: '自研AI智能引擎，支持通过自然语言指令生成开发代码，帮助用户高效完成代码任务',
                },
            ],
            btnText: '立即使用',
            imgSrc: `${basePath}/img_hexinnengli01.png`,
        },
        // 核心能力: 多模态数据管理、一站式高性能计算、智能数据分析
        abilityList: [
            {
                title: '多模态数据管理',
                content: [
                    {
                        title: '统一管理',
                        description: '整合多种数据源，统一管理结构和非结构化数据',
                    },
                    {
                        title: '智能数据治理',
                        description: '内置质量规则库，结合大模型能力，对数据质量进行智能评估和自动修复',
                    },
                    {
                        title: '企业级数据安全',
                        description: '支持数据加密、权限管理和操作审计，满足企业数据合规要求',
                    },
                ],
                imgSrc: `${basePath}/img_hexinnegli02.png`,
            },
            {
                title: '一站式高性能计算',
                content: [
                    {
                        title: '多计算引擎协同',
                        description: '支持各类大数据和AI引擎，多引擎协同计算，保障大规模并发任务调度执行',
                    },
                    {
                        title: '异构加速支持',
                        description: '提供GPU、CPU异构算力，大幅提升资源利用效率，数据读写速度显著加快',
                    },
                ],
                imgSrc: `${basePath}/img_hexinnegli03.png`,
            },
            {
                title: '智能数据分析',
                content: [
                    {
                        title: 'AI Search',
                        description: '内置智能搜索，支持检索文档、图片、视频等多模态数据，通过语义理解能力，让搜索结果更准确',
                    },
                    {
                        title: '数据洞察',
                        description: '帮助用户海量数据中提取和探索数据特征，并通过可视化图表和文本描述来直观表达数据见解',
                    },
                ],
                imgSrc: `${basePath}/img_hexinnegli04.png`,
            },
        ],
    },
    scene: {
        title: '应用场景',
        sceneList: [
            {
                title: '通用大模型训练',
                key: 'generalModel',
                tabTitle: '大模型',
                content: [
                    {
                        title: '场景痛点',
                        description: '数据分散、数据管理难、数据处理效率低、资源成本高等',
                    },
                    {
                        title: '场景方案',
                        description: 'DataBuilder提供多模态数管理能力，实现数据一致性与安全性；通过Doris实时查询分析，实现PB级数据秒级查询检索，数据处理效率提升6倍；采用iceberg列存方案，大幅降低模型训练的资源成本',
                    },
                ],
                btnText: '立即使用',
                imgSrc: `${basePath}/img_yingyongchangjing01.png`,
            },
            {
                title: '智能驾驶',
                key: 'smartDriving',
                tabTitle: '智驾',
                content: [
                    {
                        title: '场景痛点',
                        description: '海量数据分散格式多样、数据变更多版本不一致、手动处理车端采集数据效率低等',
                    },
                    {
                        title: '场景方案',
                        description: 'DataBuilder提供多模态数据统一存储和管理、数据版本管理、工作流编排等能力，减少车企用户在数据管理、数据处理和模型训练环节的重复工作，提升智能驾驶模型迭代效率',
                    },
                ],
                btnText: '立即使用',
                imgSrc: `${basePath}/img_yingyongchangjing02.png`,
            },
            {
                title: 'RAG知识库',
                key: 'RAG',
                tabTitle: 'RAG',
                content: [
                    {
                        title: '场景痛点',
                        description: '数据格式复杂、数据加工处理效率低、检索效果不准确',
                    },
                    {
                        title: '场景方案',
                        description: 'DataBuilder支持对多模态数据进行解析、特征提取和理解，提供OCR、视觉识别、embedding等预置能力，也支持用户自定义数据加工算子并在平台运行。用于企业知识库检索、智能问答、推荐等场景，配合大模型，让输出结果更准确',
                    },
                ],
                btnText: '立即使用',
                imgSrc: `${basePath}/img_yingyongchangjing03.png`,
            },
            {
                title: '数据中台升级',
                key: 'upgradeDataCenter',
                tabTitle: '数据中台',
                content: [
                    {
                        title: '场景痛点',
                        description: '传统数据中台无法管理和处理非结构化数据',
                    },
                    {
                        title: '场景方案',
                        description: 'DataBuilder支持用户在Lakehouse之上基于同一份湖存储数据构建多计算引擎应用，统一管理多模态数据，同时纳管AI训推数据和模型资产，内置丰富算子，支持复杂大数据和AI任务编排，提供易用的可视化数据加工和数据分析能力，满足传统数据中台升级到智能数据平台的需求',
                    },
                ],
                btnText: '立即使用',
                imgSrc: `${basePath}/img_yingyongchangjing04.png`,
            },
        ],
    },
    guide: {
        title: '千帆数据智能平台 DataBuilder',
        description: '帮助用户提升模型训练和AI原生应用迭代效率',
    },
};
export const getServerSideProps: GetServerSideProps<PagePropsObj> = async () => {
    return {
        props: {
            headOption: {
                title: '百度智能云 - 千帆数据智能平台 DataBuilder',
            },
        },
    };
};
/** 处理顶部 header tab 自动定位 */
const handleHeaderTabAutoLocation = (key: ModuleKey) => {
    const targetElement = document.querySelector(`[data-header-module-item-key="${key}"]`) as unknown as HTMLDivElement;
    const container = document.querySelector('.mobile-header-module-list-container') as unknown as HTMLDivElement;

    if (!targetElement || !container) {
        return;
    }

    // 获取容器的滚动位置
    const scrollLeft = container.scrollLeft;
    // 获取目标元素的位置
    const targetElementOffsetLeft = targetElement.offsetLeft;
    // 获取目标元素的宽度
    const targetElementWidth = targetElement.offsetWidth;
    // 获取容器的宽度
    const containerWidth = container.offsetWidth;

    // 计算需要滚动的距离
    const scrollDistance = targetElementOffsetLeft + targetElementWidth - containerWidth;

    // 如果目标元素已经在可视区域内，则不需要滚动
    if (scrollLeft < targetElementOffsetLeft || targetElementOffsetLeft + targetElementWidth < scrollLeft + containerWidth) {
        // 滚动到目标元素
        container.scrollTo({
            left: scrollDistance,
            behavior: 'smooth',
        });
    }
};
/** 判断是否在视窗上方 */
const isAboveViewport = (element: HTMLDivElement, offset: number = 0) => {
    if (!element) {
        return false;
    }

    const rect = element.getBoundingClientRect();

    return rect.top < offset;
};
/** 判断是否在视窗下方 */
const isBelowViewport = (element: HTMLDivElement) => {
    if (!element) {
        return false;
    }

    const rect = element.getBoundingClientRect();

    return rect.top > window.innerHeight;
};

export default function ProductsIndex() {
    // 当前的设备类型
    const [deviceType, setDeviceType] = useState<DeviceType>(DeviceType.Desktop);
    // 是否小于移动端尺寸 - 隐藏某些内容
    const isSmallerThanMobile = useMemo(() => {
        return deviceType === DeviceType.Mobile || deviceType === DeviceType.SmMdMobile;
    }, [deviceType]);

    const {isPC} = useDeviceType();
    // 屏幕宽度
    const [screenWidth, setScreenWidth] = useState<number>(0);

    // banner 区域偏移量
    const [bannerMarginTop, setBannerMarginTop] = useState(0);
    /** 当前选中的 Header Tab Key */
    const [currentHeaderTabKey, setCurrentHeaderTabKey] = useState<ModuleKey>(ModuleKey.Structure);
    /** 判断 Header 是否显示 */
    const [isHeaderVisible, setIsHeaderVisible] = useState(false);
    const [headerModuleSliderStyle, setHeaderModuleSliderStyle] = useState<any>({});

    /** 是否正在点击 Header Tab */
    const isClickingHeaderTab = useRef(false);

    /** 监听点击顶部菜单 Tab */
    const onClickHeaderTabItem = useCallback((key: ModuleKey) => {
        setCurrentHeaderTabKey(key);

        const module = document.querySelector(`[data-module-key="${key}"]`);
        if (module) {
            isClickingHeaderTab.current = true;
            const offset = module.getBoundingClientRect().top + window.scrollY - 150;
            window.scrollTo({
                top: offset,
                behavior: 'smooth',
            });
            setTimeout(() => {
                isClickingHeaderTab.current = false;
            }, 500);
        }
    }, []);
    // 监听设备类型&事件监听器
    useEffect(() => {
        const titleList: HTMLDivElement[] = (document.querySelectorAll(`.${moduleTitleClassName}`) || []) as HTMLDivElement[];
        const contentList: HTMLDivElement[] = (document.querySelectorAll(`.${moduleContentClassName}`) || []) as HTMLDivElement[];
        const nodeList = [...titleList, ...contentList];
        // 获取所有的模块，用户锚点定位
        const moduleList: HTMLDivElement[] = (document.querySelectorAll('[data-module-key]') || []) as HTMLDivElement[];
        // 根据屏幕宽度设置设备类型
        const handleSetDeviceType = () => {
            const width = window.innerWidth;
            setScreenWidth(width);
            if (width <= 599) {
                setDeviceType(DeviceType.SmMdMobile);
                return DeviceType.SmMdMobile;
            }
            if (width >= 1200) {
                setDeviceType(DeviceType.Desktop);
                return DeviceType.Desktop;
            } else if (width >= 768) {
                setDeviceType(DeviceType.Tablet);
                return DeviceType.Tablet;
            } else {
                setDeviceType(DeviceType.Mobile);
                return DeviceType.Mobile;
            }
        };
        // 实现视窗下模块动画展示效果
        const handleAnimationEffect = () => {
            const _deviceType = handleSetDeviceType();
            if (_deviceType === DeviceType.Mobile || _deviceType === DeviceType.SmMdMobile) {
                nodeList.forEach(item => {
                    item.classList.remove('databuilder-below-module');
                });
                return;
            }

            nodeList.forEach(item => {
                if (isBelowViewport(item)) {
                    item.classList.add('databuilder-below-module');
                } else {
                    item.classList.remove('databuilder-below-module');
                }
            });
        };
        // 处理 banner 区域
        const handleBannerOffset = () => {
            const header = document.querySelector('.cloud-header-pc');
            if (header) {
                setBannerMarginTop(header.getBoundingClientRect().height * -1);
            }
        };
        // 处理锚点定位
        const handleAnchorLocation = () => {
            const header = document.querySelector('.cloud-header-pc') as unknown as HTMLDivElement;
            // pc 端判断
            if (header) {
                const headerHight = header.getBoundingClientRect().height;
                if (isAboveViewport(header, headerHight * -1)) {
                    setIsHeaderVisible(true);
                } else {
                    setIsHeaderVisible(false);
                }
            } else {
                // 移动端判断
                const _deviceType = handleSetDeviceType();
                if (_deviceType === DeviceType.Mobile || _deviceType === DeviceType.SmMdMobile) {
                    const advantageNode = document.querySelector(`[data-module-key="${ModuleKey.Structure}"]`);
                    if (advantageNode) {
                        setIsHeaderVisible(isAboveViewport(advantageNode as unknown as HTMLDivElement, 200));
                    }
                }
                else {
                    setIsHeaderVisible(false);
                }
            }

            if (isClickingHeaderTab.current) {
                return;
            }

            const _moduleList = Array.from(moduleList).filter(item => !isBelowViewport(item) && !isAboveViewport(item));
            if (_moduleList.length) {
                const first = _moduleList[0];
                const moduleKey = first.getAttribute('data-module-key');
                const targetModuleKey = parseInt(moduleKey, 10) as ModuleKey;
                setCurrentHeaderTabKey(targetModuleKey);
                handleHeaderTabAutoLocation(targetModuleKey);
            }
        };
        const throttleHandleAnimationEffect = throttle(handleAnimationEffect, 100);
        const throttleHandleSetDeviceType = throttle(handleSetDeviceType, 200);
        const throttleHandleAnchorLocation = throttle(handleAnchorLocation, 200);
        const throttleHandleBannerOffset = throttle(handleBannerOffset, 200);
        handleBannerOffset();
        handleSetDeviceType();
        handleAnchorLocation();
        handleAnimationEffect();
        window.addEventListener('scroll', throttleHandleAnimationEffect);
        window.addEventListener('scroll', throttleHandleAnchorLocation);
        window.addEventListener('resize', throttleHandleAnimationEffect);
        window.addEventListener('resize', throttleHandleSetDeviceType);
        window.addEventListener('resize', throttleHandleAnchorLocation);
        window.addEventListener('resize', throttleHandleBannerOffset);
        return () => {
            window.removeEventListener('scroll', throttleHandleAnimationEffect);
            window.removeEventListener('scroll', throttleHandleAnchorLocation);
            window.removeEventListener('resize', throttleHandleAnimationEffect);
            window.removeEventListener('resize', throttleHandleAnchorLocation);
            window.removeEventListener('resize', throttleHandleSetDeviceType);
            window.removeEventListener('resize', throttleHandleBannerOffset);
        };
    }, []);
    // 监听 Header Tab 自动定位
    useEffect(() => {
        const node = document.querySelector(`[data-header-module-key="${currentHeaderTabKey}"]`);
        if (!node) {
            return;
        }
        const {left: parentNodeLeft} = node.parentElement.getBoundingClientRect();
        const {width: nodeWidth, left: nodeLeft} = node.getBoundingClientRect();
        const left = nodeLeft - parentNodeLeft;
        setHeaderModuleSliderStyle({
            width: `${nodeWidth}PX`,
            left: `${left}PX`,
        });
    }, [currentHeaderTabKey]);


    // 校验用户是否登录
    const [, , loginHandler] = useCheckUserinfo({
        isImmediately: false,
        maskClosable: true,
    });
    // 埋点监听 - 立即使用按钮的点击
    const onClickUseBtn = useCallback((name?: string, link?: string) => {
        loginHandler(info => {
            if (info.hasLogin) {
                sendMonitor({
                    category: '立即使用',
                    action: 'click',
                    name: 'Databuilder-首页-使用',
                    value: '立即使用',
                });
                // 融合态，目前由EDAP处理开通和购买逻辑
                window.open(link
                    || 'https://console.bce.baidu.com/edap/#?from=db', '_blank');
                // 测试环境 - 沙盒
                // window.open(link
                //     || 'https://qasandbox.bcetest.baidu.com/edap/#?from=db', '_blank');
            }
        });
    }, [loginHandler]);
    // 埋点监听 - PlayGround按钮的点击
    const onClickPlayBtn = useCallback(async () => {
        // 1、先判断用户是否登录，未登录的话，参考其他业务处理，例如提示用户登录或引导用户去登录
        // loginHandler(async info => {
        //     if (info.hasLogin) {
                try {
                    // 2、已登录的话，判断用户是否实名，未实名的话，提示用户实名或引导用户去实名
                    const realNameRes = await netService.post<null, any>(urlConst.ACCOUNT_REAL_NAME_TYPE);

                    if (realNameRes.success) {
                        // 检查实名状态，如果未实名则显示实名弹窗
                        if (realNameRes.result === null) {
                            // 未实名，显示实名弹窗
                            const dy: UDynamicService = Ioc(UDynamicService);
                            dy.open({
                                component: URealNameModal,
                                props: {
                                    maskClosable: false,
                                    hideCloseIcon: false,
                                    onOk: () => {
                                        // 实名成功后重新执行PlayGround逻辑
                                        onClickPlayBtn();
                                    },
                                    onCancel: () => {
                                        // 用户取消实名
                                    },
                                },
                            });
                            return;
                        }
                    }

                    // 3、调用接口判断用户是否已经激活了playground
                    const playgroundRes = await getPlaygroundActive();

                    if (playgroundRes.result) {
                        // 已激活，直接跳转
                        sendMonitor({
                            category: 'PlayGround',
                            action: 'click',
                            name: 'Databuilder-首页-PlayGround',
                            value: '已激活跳转',
                        });
                        window.open('https://qasandbox.bcetest.baidu.com/edap', '_blank');
                    } else {
                        // 4、如果未激活，调用激活接口
                        const activateRes = await activatePlayground();

                        if (activateRes.result) {
                            // 激活成功，显示成功弹窗
                            sendMonitor({
                                category: 'PlayGround',
                                action: 'click',
                                name: 'Databuilder-首页-PlayGround',
                                value: '激活成功',
                            });

                            const dy: UDynamicService = Ioc(UDynamicService);
                            dy.open({
                                component: UConfirm,
                                props: {
                                    title: '激活成功',
                                    content: '恭喜您激活成功，请到邮件中查看登录地址与账号密码',
                                    okText: '知道了',
                                    hideCancel: true,
                                    maskClosable: true,
                                    onOk: () => {
                                        // 弹窗关闭
                                    },
                                },
                            });
                        } else {
                            // 激活失败
                            message.error('激活失败，请稍后重试');
                        }
                    }
                } catch (error) {
                    console.error('PlayGround处理失败:', error);
                    message.error('操作失败，请稍后重试');
                }
            // }
    //     });
    }, [loginHandler]);

    // 埋点监听 - 私有化部署咨询按钮的点击
    const onClickQueryBtn = useCallback(() => {
        sendMonitor({
            category: '私有化部署咨询',
            action: 'click',
            name: 'Databuilder-首页-咨询',
            value: '私有化部署咨询',
        });
        window.open('https://cloud.baidu.com/survey/databuilderapply.html', '_blank');
    }, []);
    // 埋点监听 - 帮助文档按钮的点击
    const onClickDocBtn = useCallback(() => {
        sendMonitor({
            category: '帮助文档',
            action: 'click',
            name: 'Databuilder-首页-文档',
            value: '帮助文档',
        });
        window.open('https://cloud.baidu.com/doc/DataBuilder/index.html', '_blank');
    }, []);

    // 应用场景tab相关
    const [currentSceneTabKey, setCurrentSceneTabKey] = useState<string>('generalModel');

    /** 当前选中的 场景 Tab 索引 */
    const currentSceneTabIndex = useMemo(() => {
        return config.scene.sceneList.findIndex(item => item.key === currentSceneTabKey);
    }, [currentSceneTabKey]);
    /** 场景 Tab slider 的偏移量 */
    const [sceneTabSliderOffset, setSceneTabSliderOffset] = useState<number>(0);
    useEffect(() => {
        const fn = () => {
            let offset = 0;
            const gap = isSmallerThanMobile ? 10 : 0;

            if (currentSceneTabIndex === 0) {
                offset = -4;
            } else {
                for (let i = 0; i < currentSceneTabIndex; i++) {
                    const node = document.querySelector(`[data-scene-tab-index="${i}"]`);
                    if (node) {
                        offset += node.getBoundingClientRect().width;
                        offset += gap;
                    }
                }
            }
            offset += 4;
            setSceneTabSliderOffset(offset);
        };

        fn();

        const throttledFn = throttle(fn, 300);

        window.addEventListener('resize', throttledFn);

        return () => {
            window.removeEventListener('resize', throttledFn);
        };
    }, [currentSceneTabIndex, isSmallerThanMobile]);
    return (
        <>
            <Head>
                <meta name="viewport" content="width=device-width, user-scalable=0, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"></meta>
            </Head>

            {/* 吸顶导航 */}
            {
                <div className={cx(styles['fixed-header-container'], {
                    [styles['fixed-header-container-hidden']]: !isHeaderVisible,
                })}
                >
                    <div className={styles['fixed-header-inner-container']}>
                        <div className={styles['left-container']}>
                            {config.header.productName}
                        </div>
                        <div className={styles['header-module-list-container']}>
                            {
                                config.header.moduleList.map(({name, key}) => (
                                    <div
                                        className={cx(styles['header-module-item'], {
                                            [styles['header-active-module-item']]: key === currentHeaderTabKey,
                                        })}
                                        key={key}
                                        data-header-module-key={key}
                                        onClick={() => onClickHeaderTabItem(key)}
                                    >
                                        <div className={styles['header-module-text']}>{name}</div>
                                    </div>
                                ))
                            }
                            <div
                                id="header-module-slider"
                                style={{
                                    position: 'absolute',
                                    bottom: 0,
                                    width: headerModuleSliderStyle.width,
                                    height: '2PX',
                                    backgroundColor: '#2468f2',
                                    left: headerModuleSliderStyle.left,
                                    transition: '.3s left linear',
                                }}
                            >
                            </div>
                        </div>
                        <div className={styles['right-container']} onClick={() => onClickUseBtn()}>{config.freeBtnText}</div>
                    </div>
                </div>
            }
            {/* banner */}
            <div
                className={cx(styles['banner-advantage-module-container'], moduleClassName)}
                style={{
                    marginTop: isSmallerThanMobile ? '0' : bannerMarginTop + 'px',
                    paddingTop: isSmallerThanMobile ? '0' : (Math.max(-bannerMarginTop, 63) - 63 + 'px'),
                }}
            >
                <div
                    className={cx(styles['banner-module-container'], moduleClassName)}
                    style={{
                        paddingTop: isSmallerThanMobile ? '0' : Math.min(-bannerMarginTop, 63) + 'px',
                    }}
                >
                    <div className={cx(styles['banner-module-content'], moduleContentClassName)}>
                        <div className={styles['banner-title']}>{config.banner.title}</div>
                        <div className={styles['banner-description']}>{config.banner.description}</div>
                        <div className={cx(styles['banner-btns'])}>
                            <div className={cx(styles['free-btn'], styles['common-btn'])} onClick={() => onClickUseBtn()}>
                                {config.freeBtnText}
                            </div>
                            {/* 根据设备类型显示不同按钮 PC 端会显示 PlayGround */}
                            {isPC ? (
                                <>
                                    <div className={cx(styles['doc-btn'], styles['common-btn'])} onClick={() => onClickPlayBtn()}>
                                        <div className={styles['btn-text']}>{config.playBtnText}</div>
                                    </div>
                                    <div className={cx(styles['text-btn'])} onClick={() => onClickQueryBtn()}>
                                        {config.queryBtnText}
                                    </div>
                                </>
                            ) : (
                                <div className={cx(styles['doc-btn'], styles['common-btn'])} onClick={() => onClickQueryBtn()}>
                                    <div className={styles['btn-text']}>{config.queryBtnText}</div>
                                </div>
                            )}
                            <div className={cx(styles['text-btn'])} onClick={() => onClickDocBtn()}>
                                {config.docBtnText}
                            </div>
                        </div>
                    </div>
                    {/* 移动端的产品优势内容 */}
                    {
                        isSmallerThanMobile && <div className={cx(styles['mobile-advantage-container'], moduleClassName)}>
                            {
                                config.banner.advantageList.map(({title, highlightText, highlightBefore}) => (
                                    <div className={styles['mobile-advantage-item-container']} key={title}>
                                        {title && <div className={styles['advantage-title']}>{title}</div>}
                                        {highlightBefore && <div className={cx(styles['advantage-highlight-text'],
                                            {[styles['advantage-top-font']]: !title}, {[styles['advantage-highlight-font']]: title})}
                                        >{highlightBefore}</div>}
                                        {highlightText && <div
                                            className={cx(styles['advantage-highlight-text'], styles['advantage-highlight-font'])}
                                        >{highlightText}</div>}
                                    </div>
                                ))
                            }
                        </div>
                    }
                </div>
                {/* 非移动端的产品优势内容 */}
                {
                    isSmallerThanMobile || <div className={cx(styles['advantage-module-container'], moduleClassName)}>
                        {
                            config.banner.advantageList.map(({highlightBefore, title, highlightText, description}) => (
                                <div className={styles['advantage-item-container']} key={title}>
                                    <div className={styles['top-container']}>
                                        {
                                            highlightBefore && <div
                                                className={cx(styles['advantage-highlight-text'], styles['left-text'])}
                                            >{highlightBefore}</div>}
                                        {title && <div className={styles['advantage-title']}>{title}</div>}
                                        {highlightText && <div
                                            className={cx(styles['advantage-highlight-text'], styles['right-text'])}
                                        >{highlightText}</div>}
                                    </div>
                                    {isSmallerThanMobile || <div className={styles['advantage-description']}>{description}</div>}
                                </div>
                            ))
                        }
                    </div>
                }
                {/* 能力覆盖 */}
                <div className={cx(styles['structure-module-container'], moduleClassName)}>
                    <div className={styles['structure-module-inner-container']}>
                        <div
                            className={cx(styles['structure-module-title'], moduleTitleClassName)}
                            data-module-key={ModuleKey.Structure}
                        >
                            {config.structure.title}
                        </div>
                        <div className={cx(styles['structure-module-content'], moduleContentClassName)}>
                            <img className={styles['structure-img']} src={config.structure.imgSrc} alt={config.structure.title} />
                        </div>
                    </div>

                </div>
            </div>

            {/* 核心能力 */}
            <div className={cx(styles['core-ability-module-container'], moduleClassName)}>
                <div
                    className={cx(styles['core-ability-module-title'], moduleTitleClassName)}
                    data-module-key={ModuleKey.Ability}
                >{config.ability.title}
                </div>
                <div className={cx(styles['core-ability-module-content'], moduleContentClassName)}>
                    <div className={cx(styles['iteration-container'], moduleContentClassName)}>
                        <div className={styles['left-container']}>
                            <div className={styles['left-title']}>
                                {config.ability.iteration.title}
                            </div>
                            <div className={styles['card-container']}>
                                {config.ability.iteration.cardList.map(({title, icon, description}) => (
                                    <div className={styles['left-card']} key={title}>
                                        <div className={styles['card-title']}>
                                            <img src={icon} alt={title} className={styles['card-icon']} />
                                            {title}
                                        </div>
                                        <div className={styles['left-description']}>
                                            {description}
                                        </div>
                                    </div>
                                ))}
                            </div>
                            <div className={cx(styles['free-btn'], styles['common-btn'])}>{config.ability.iteration.btnText}</div>
                        </div>
                        <img className={styles['right-img']} src={config.ability.iteration.imgSrc} alt={config.ability.iteration.title} />
                    </div>
                    <div className={cx(styles['abilitylist-container'], moduleContentClassName)}>
                        {config.ability.abilityList.map(({title, content, imgSrc}) => (
                            <div key={title} className={styles['item-container']}>
                                <div className={styles['ability-item']}>
                                    <div className={styles['left-title']}>{title}</div>
                                    <div className={styles['content-container']}>
                                        {content.map(({title, description}) => (
                                            <div key={title} className={styles['content-item']}>
                                                <div className={styles['content-item-title']}>
                                                    <span className={styles['dot']}></span>
                                                    <span className={styles['text']}>{title}</span>
                                                </div>
                                                <div className={styles['content-item-description']}>{description}</div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                                <img className={styles['item-image']} src={imgSrc} alt={title} />
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            {/* 应用场景 */}
            <div className={cx(styles['apply-scene-module-container'], moduleClassName)}>
                <div className={styles['scene-module-square-bg-container']}></div>
                <div
                    className={cx(styles['apply-scene-module-title'], moduleTitleClassName)}
                    data-module-key={ModuleKey.Scene}
                >{config.scene.title}
                </div>
                <div className={cx(styles['scene-tabs-container'], moduleContentClassName)}>
                    {
                        config.scene.sceneList.map(({title, key, tabTitle}, index) => (
                            <div
                                className={cx(styles['scene-tab-item'], {
                                    [styles['scene-active-tab-item']]: key === currentSceneTabKey,
                                })}
                                key={key}
                                data-scene-tab-index={index}
                                onClick={() => setCurrentSceneTabKey(key)}
                            >
                                {screenWidth < 445 ? tabTitle : title}
                            </div>
                        ))
                    }
                    <div
                        className={styles['scene-tab-item-slider']}
                        style={{
                            left: `${sceneTabSliderOffset}PX`,
                            ...(currentSceneTabIndex === 0 ? {} : {
                                transform: 'none',
                            }),
                        }}
                    >
                    </div>
                </div>
                <div className={cx(styles['scene-content-container'], moduleContentClassName)}>
                    {
                        config.scene.sceneList.map(({key, title, content, imgSrc, btnText}) => (
                            <div
                                className={styles['scene-item-content-container']}
                                key={title}
                                style={{
                                    display: key === currentSceneTabKey ? 'flex' : 'none',
                                    width: key === currentSceneTabKey ? '100%' : '0',
                                    visibility: key === currentSceneTabKey ? 'visible' : 'hidden',
                                }}
                            >
                                <div className={styles['left-container']}>
                                    <div className={styles['scene-content-title']}>{title}</div>
                                    <div className={styles['scene-content-description-container']}>
                                        {
                                            content.map(item => (
                                                <>
                                                    <div className={styles['scene-content-title-description']} key={item.title}>
                                                        {item.title}
                                                    </div>
                                                    <div className={styles['scene-content-normal-description']} key={item.description}>
                                                        {item.description}
                                                    </div>
                                                </>
                                            ))
                                        }
                                    </div>
                                    <div className={cx(styles['free-btn'], styles['common-btn'])} onClick={() => onClickUseBtn()}>{btnText}</div>
                                </div>
                                <div className={styles['right-container']}>
                                    {<img src={imgSrc} className={styles['scene-content-img']} />}
                                </div>
                            </div>
                        ))
                    }
                </div>
            </div>
            {/* 使用引导 */}
            <div className={cx(styles['guide-part-module-container'], moduleClassName)}>
                <div
                    className={cx(styles['guide-part-module-title'], moduleTitleClassName)}
                    data-module-key={ModuleKey.Guide}
                >{config.guide.title}
                </div>
                <div className={cx(styles['guide-description'], moduleContentClassName)}>{config.guide.description}</div>
                <div className={cx(styles['guide-btns'], moduleContentClassName)}>
                    <div className={cx(styles['free-btn'], styles['common-btn'])} onClick={() => onClickUseBtn()}>
                        {config.freeBtnText}
                    </div>
                    <div className={cx(styles['doc-btn'], styles['common-btn'])} onClick={() => onClickDocBtn()}>
                        <div className={styles['btn-text']}>{config.docBtnText}</div>
                    </div>
                </div>
            </div>
        </>
    );
}
