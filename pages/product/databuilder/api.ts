/* eslint-disable */

import axios, {AxiosInstance} from 'axios';

// 响应接口定义
export interface PlaygroundActiveResponse {
    code: string;
    requestId: string;
    result: boolean; // 是否激活
}

// 创建axios实例，baseURL为https://console.bce.baidu.com，请求头中加入x-region: bd
const dataBuilderApi: AxiosInstance = axios.create({
    baseURL: 'https://console.bce.baidu.com',
    headers: {
        'x-region': 'bd',
        'Content-Type': 'application/json'
    }
});

// 创建支持跨域的axios实例，用于从cloud.baidu.com域名访问
const crossDomainApi: AxiosInstance = axios.create({
    baseURL: 'https://console.bce.baidu.com',
    headers: {
        'x-region': 'bd',
        'Content-Type': 'application/json'
    },
    withCredentials: true
});

// 检测当前域名是否为cloud.baidu.com
const isCloudDomain = () => {
    if (typeof window === 'undefined') return false;
    return window.location.hostname === 'cloud.baidu.com';
};

// 获取playground激活状态
export const getPlaygroundActive = async (): Promise<PlaygroundActiveResponse> => {
    try {
        let response;
        if (isCloudDomain()) {
            // 从cloud.baidu.com域名访问，使用支持跨域的axios实例
            response = await crossDomainApi.get<PlaygroundActiveResponse>('/api/databuilder/v1/playground/active');
        } else {
            // 其他域名直接调用原接口
            response = await dataBuilderApi.get<PlaygroundActiveResponse>('/api/databuilder/v1/playground/active');
        }
        return response.data;
    } catch (error) {
        console.error('获取playground激活状态失败:', error);
        throw error;
    }
};

// 激活用户体验账户响应接口定义
export interface ActivatePlaygroundResponse {
    code: string;
    requestId: string;
    result: boolean; // 是否激活成功
    [property: string]: any;
}

// 激活用户体验账户
export const activatePlayground = async (): Promise<ActivatePlaygroundResponse> => {
    try {
        let response;
        if (isCloudDomain()) {
            // 从cloud.baidu.com域名访问，使用支持跨域的axios实例
            response = await crossDomainApi.post<ActivatePlaygroundResponse>('/api/databuilder/v1/playground');
        } else {
            // 其他域名直接调用原接口
            response = await dataBuilderApi.post<ActivatePlaygroundResponse>('/api/databuilder/v1/playground');
        }
        return response.data;
    } catch (error) {
        console.error('激活用户体验账户失败:', error);
        throw error;
    }
};

// 导出axios实例，供其他API使用
export default dataBuilderApi;