/* eslint-disable complexity */
import next from 'next';
import Ko<PERSON> from 'koa';
import {UNetService} from '@baidu/bce-services';
import {Ioc} from '@baidu/bce-decorators';

// config
import {config} from './config/base';

// middleware
import {requestUrlFix} from './middleware/reqUrlFix';
import {isMobile} from './middleware/isMobile';
import {reqSpendTimeRecord} from './middleware/reqSpendTimeRecord';
import {locale} from './middleware/locale';
import {traceId} from './middleware/traceId';
import {cors} from './middleware/cors';

// router
import router from './routes';

// utils
import {logger} from './utils/logger';
import {UNetInterceptor} from './utils/netInterceptor';
import {isDev} from './utils/env';

// handler
import {proAndSolutionHandler} from './handler/proAndSolutionHandler';
import {docSubIndexHandler} from './handler/docSubIndexHandler';
import {surveyHandler} from './handler/surveyHandler';
import {summitHandler} from './handler/summitHandler';
import {homeHandler} from './handler/homeHandler';
import {productsHandler} from './handler/productsHandler';

// 数据库
import {dbInit} from './db/database';

const port = parseInt(process.env.PORT || '9889', 10);

const app = next({dev: isDev});
const handle = app.getRequestHandler();

dbInit();
app.prepare().then(() => {
    const server = new Koa();

    // 添加axios拦截器
    UNetService.setInterceptors([Ioc(UNetInterceptor)]);

    server.use(cors);
    server.use(traceId);
    server.use(locale);
    server.use(isMobile);
    server.use(requestUrlFix);
    server.use(reqSpendTimeRecord);

    server.use(router.routes());

    router.all('(.*)', async ctx => {
        if (
            /^(\/product|\/solution)\//.test(ctx.path)
            && !config.proAndSolutionRoutes.some((item: string) => item === ctx.path)
        ) {
            const res = await proAndSolutionHandler(ctx, app);
            if (res) {
                return;
            }
        } else if (ctx.path.startsWith('/product-s/')) {
            const res = await productsHandler(ctx, app);
            if (res) {
                return;
            }
        } else if (
            /^(\/doc\/(\w|-)+\/index)/.test(ctx.path)
            && !config.docSubIndexRoutes.some((item: string) => item === ctx.path)
        ) {
            const res = await docSubIndexHandler(ctx, app);
            if (res) {
                return;
            }
        } else if (/^\/summit(\/\w+){1,2}\/(index|showroom|index_new|index_live)$/.test(ctx.path)) {
            const res = await summitHandler(ctx, app);
            if (res) {
                return;
            }
        } else if (/^\/(survey|survey_summit)\/(.+)/.test(ctx.path)) {
            const res = await surveyHandler(ctx, app);
            if (res) {
                return;
            }
        } else if (ctx.path === '/index') {
            await homeHandler(ctx, app);
        } else {
            await handle(ctx.req, ctx.res);
        }
        // 用于禁用 Koa 的自动响应机制，适用于需要手动控制响应的场景。使用时需确保手动发送响应，避免客户端无响应
        ctx.respond = false;
    });


    server.on('error', err => {
        logger.error({msg: 'next server error', err});
    });

    // 中间件捕捉不到的错误 这里可以拦截
    process.on('uncaughtException', err => {
        logger.error({msg: 'next app uncaughtException', err});
    });

    process.on('unhandledRejection', (reason, p) => {
        logger.error({msg: 'next app Unhandled Rejection', promise: p, reason});
    });

    server.listen(port, () => {
        logger.info({msg: `Ready on http://localhost:${port}`});
    });
});
