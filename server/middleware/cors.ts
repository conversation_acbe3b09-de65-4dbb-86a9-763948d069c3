/**
 * @file CORS 跨域中间件
 */

import {DefaultContext, DefaultState, Middleware} from 'koa';

export const cors: Middleware<DefaultState, DefaultContext> = async (ctx, next) => {
    // 设置 CORS 头部，允许 cloud.baidu.com 域名访问
    const origin = ctx.get('Origin');
    
    // 允许的域名列表
    const allowedOrigins = [
        'https://cloud.baidu.com',
        'https://cloudtest.baidu.com',
        'http://localhost:3000',
        'http://localhost:9889'
    ];

    if (allowedOrigins.includes(origin)) {
        ctx.set('Access-Control-Allow-Origin', origin);
    }

    ctx.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    ctx.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-region, X-Requested-With');
    ctx.set('Access-Control-Allow-Credentials', 'true');
    ctx.set('Access-Control-Max-Age', '86400'); // 24小时

    // 处理 OPTIONS 预检请求
    if (ctx.method === 'OPTIONS') {
        ctx.status = 200;
        return;
    }

    await next();
};
